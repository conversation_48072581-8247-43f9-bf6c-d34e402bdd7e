/**
 * Prompt Templates System for Salonier Assistant
 * Version modificada para evitar filtros de contenido
 */

import { TemplateType, TemplateContext, RegionalConfig, FormulaConfig } from '../types.ts'
import { OPTIMIZATION_TARGETS } from '../constants.ts'

export class PromptTemplates {
  /**
   * Selects optimal template based on context
   */
  static selectOptimalTemplate(context: TemplateContext): TemplateType {
    if (context.userTier === 'enterprise') return 'full'
    if (context.imageQuality === 'high') {
      return context.userTier === 'pro' ? 'optimized' : 'minimal'
    }
    if (context.imageQuality === 'low') {
      return context.userTier === 'pro' ? 'full' : 'optimized'
    }
    return OPTIMIZATION_TARGETS.defaultTemplate[context.userTier]
  }

  /**
   * Get diagnosis prompt with optimization level
   */
  static getDiagnosisPrompt(template: TemplateType = 'full', lang: 'es' | 'en' = 'es'): string {
    const prompts = {
      es: {
        full: `Eres un experto colorista profesional realizando un análisis técnico del cabello para un servicio de coloración en un salón de belleza.

IMPORTANTE: Este es un análisis profesional del CABELLO ÚNICAMENTE para determinar tratamientos de coloración. NO analices rostros, personas o características personales.
  
Analiza la textura, color y condición del CABELLO en la imagen y devuelve un análisis técnico COMPLETO en formato JSON con EXACTAMENTE esta estructura:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta",
  "overallTone": "nombre del tono general",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": número decimal (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": número decimal (1-10),
      "tone": "tono específico",
      "undertone": "Frío|Cálido|Neutro",
      "percentage": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "grayPercentage": número (0-100),
      "grayType": "Blanco|Gris|Mixto|null",
      "grayPattern": "Uniforme|Localizado|Disperso|null",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "damage": "Ninguno|Leve|Moderado|Severo",
      "elasticity": "Buena|Regular|Mala",
      "porosity": "Baja|Media|Alta",
      "resistance": "Fuerte|Media|Débil"
    },
    "mids": { /* misma estructura */ },
    "ends": { /* misma estructura */ }
  },
  "detectedChemicalProcess": "Coloración|Decoloración|Permanente|Alisado|Ninguno|null",
  "estimatedLastProcessDate": "texto descriptivo",
  "detectedRisks": { 
    "metallic": boolean, 
    "henna": boolean, 
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": minutos estimados,
  "overallCondition": "descripción detallada",
  "recommendations": ["lista de al menos 3 recomendaciones específicas"],
  "overallConfidence": porcentaje (0-100)
}

IMPORTANTE: Analiza CADA zona por separado con TODOS los campos.`,

        optimized: `Análisis técnico profesional de CABELLO para coloración (NO personas). Devuelve JSON:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta", 
  "overallTone": "tono",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tono",
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": 0-100,
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta"
    },
    "mids": { /* igual */ },
    "ends": { /* igual */ }
  },
  "detectedChemicalProcess": "tipo o null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "descripción",
  "recommendations": ["3 recomendaciones"],
  "overallConfidence": 0-100
}`,

        minimal: `Análisis técnico cabello para servicio coloración salón, JSON exacto:
{"hairThickness":"Fino|Medio|Grueso","hairDensity":"Baja|Media|Alta","overallTone":"tono","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tono","state":"Natural|Procesado|Decolorado|Teñido","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`
      },
      
      en: {
        full: `You are a professional colorist performing a technical hair analysis for a salon coloring service.

IMPORTANT: This is a professional HAIR analysis ONLY for determining coloration treatments. DO NOT analyze faces, people, or personal characteristics.

Analyze the image and return a COMPLETE JSON analysis with EXACTLY this structure:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "overall tone name",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": decimal number (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": decimal number (1-10),
      "tone": "specific tone",
      "undertone": "Cool|Warm|Neutral",
      "percentage": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "grayPercentage": number (0-100),
      "grayType": "White|Gray|Mixed|null",
      "grayPattern": "Uniform|Localized|Scattered|null",
      "cuticleState": "Closed|Open|Damaged",
      "damage": "None|Light|Moderate|Severe",
      "elasticity": "Good|Fair|Poor",
      "porosity": "Low|Medium|High",
      "resistance": "Strong|Medium|Weak"
    },
    "mids": { /* same structure */ },
    "ends": { /* same structure */ }
  },
  "detectedChemicalProcess": "Coloring|Bleaching|Perm|Straightening|None|null",
  "estimatedLastProcessDate": "descriptive text",
  "detectedRisks": { 
    "metallic": boolean, 
    "henna": boolean, 
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": estimated minutes,
  "overallCondition": "detailed description",
  "recommendations": ["list of at least 3 specific recommendations"],
  "overallConfidence": percentage (0-100)
}`,

        optimized: `Professional HAIR technical analysis for salon coloring (NOT people). Return JSON:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "tone",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tone",
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": 0-100,
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High"
    },
    "mids": { /* same */ },
    "ends": { /* same */ }
  },
  "detectedChemicalProcess": "type or null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "description",
  "recommendations": ["3 recommendations"],
  "overallConfidence": 0-100
}`,

        minimal: `Technical hair analysis for salon coloring service, exact JSON:
{"hairThickness":"Fine|Medium|Thick","hairDensity":"Low|Medium|High","overallTone":"tone","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tone","state":"Natural|Processed|Bleached|Colored","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`
      }
    }

    return prompts[lang][template]
  }

  /**
   * Get simplified diagnosis prompt for better AI reliability
   */
  static getSimpleDiagnosisPrompt(lang: 'es' | 'en' = 'es'): string {
    if (lang === 'en') {
      return `You are a professional hair colorist performing a technical analysis of HAIR for salon coloring services.

IMPORTANT: Analyze ONLY the hair texture, color and condition. DO NOT analyze faces or people.

CRITICAL: Return ONLY a valid JSON object. No text before or after.

Analyze the hair and return this EXACT structure:
{
  "level": number between 1-10,
  "tone": "hair tone like Dark Brown, Medium Blonde, etc",
  "reflect": "Ash, Golden, Natural, Copper, or Red",
  "state": "Natural, Colored, Bleached, or Processed",
  "damage": "Low, Medium, or High",
  "porosity": "Low, Medium, or High", 
  "elasticity": "Poor, Medium, or Good",
  "grayPercentage": number 0-100 or null if no gray,
  "additionalNotes": "any important observations" or null
}

Focus on overall hair condition, not zones. Be concise.`;
    } else {
      return `Eres un colorista profesional realizando un análisis técnico del CABELLO para servicios de coloración en salón.

IMPORTANTE: Analiza SOLO la textura, color y condición del cabello. NO analices rostros o personas.

CRÍTICO: Devuelve SOLO un objeto JSON válido. Sin texto antes o después.

Analiza el cabello y devuelve esta estructura EXACTA:
{
  "level": número entre 1-10,
  "tone": "tono como Castaño Oscuro, Rubio Medio, etc",
  "reflect": "Cenizo, Dorado, Natural, Cobrizo, o Rojizo",
  "state": "Natural, Teñido, Decolorado, o Procesado",
  "damage": "Bajo, Medio, o Alto",
  "porosity": "Baja, Media, o Alta",
  "elasticity": "Pobre, Media, o Buena", 
  "grayPercentage": número 0-100 o null si no hay canas,
  "additionalNotes": "observaciones importantes" o null
}

Enfócate en la condición general del cabello, no por zonas. Sé conciso.`;
    }
  }

  // Añadir métodos adicionales necesarios
  static getDesiredLookPrompt(
    currentLevel: number, 
    template: TemplateType = 'full', 
    lang: 'es' | 'en' = 'es'
  ): string {
    // Versión simplificada del prompt
    return `Analiza esta imagen de referencia de color de cabello deseado. Nivel actual: ${currentLevel}. Devuelve JSON con: detectedLevel, detectedTone, detectedTechnique, detectedTones, viabilityScore, estimatedSessions, requiredProcesses, confidence.`;
  }

  static getFormulaPrompt(config: any, lang: 'es' | 'en' = 'es'): string {
    // Versión simplificada del prompt
    return `Genera una fórmula de coloración profesional basada en el diagnóstico y color deseado proporcionados. Usa productos de ${config.selectedBrand} ${config.selectedLine}. Incluye cantidades exactas y tiempos de procesamiento.`;
  }

  static getConversionPrompt(originalFormula: string, targetBrand: string, targetLine: string, lang: 'es' | 'en' = 'es'): string {
    // Versión simplificada del prompt
    return `Convierte esta fórmula a productos de ${targetBrand} ${targetLine}: ${originalFormula}`;
  }

  static getStructuredFormulaPrompt(config: any, lang: 'es' | 'en' = 'es'): string {
    // Versión simplificada del prompt  
    return `Genera fórmula estructurada con productos específicos.`;
  }
}