import React, { useState, useCallback, useRef } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, FlatList, Platform, Alert } from "react-native";
import { Link, router, useFocusEffect } from "expo-router";
import { Search, PlusCircle, Package, BarChart } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { useInventoryStore } from "@/stores/inventory-store";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { useRegionalUnits } from "@/hooks/useRegionalUnits";
import { usePermissions } from "@/hooks/usePermissions";
import InventoryListItem from "@/components/inventory/InventoryListItem";
import { Product } from "@/types/inventory";

export default function InventoryScreen() {
  const [searchQuery, setSearchQuery] = useState("");
  
  // FlatList ref for scroll to top on focus
  const flatListRef = useRef<FlatList>(null);
  
  const { products, deleteProduct, searchProducts } = useInventoryStore();
  const { configuration } = useSalonConfigStore();
  const { formatCurrency, formatVolume, formatWeight, getUnitLabel } = useRegionalUnits();
  const { can } = usePermissions();
  
  // Scroll to top when screen receives focus
  useFocusEffect(
    useCallback(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      return () => {};
    }, [])
  );


  const filteredInventory = searchQuery ? searchProducts(searchQuery) : products;

  const handleDeleteItem = useCallback((id: string, name: string) => {
    Alert.alert(
      'Eliminar Producto',
      `¿Estás seguro de que quieres eliminar "${name}"?\n\nEsta acción no se puede deshacer y eliminará también todo el historial de movimientos asociado.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive', 
          onPress: () => {
            deleteProduct(id);
            // Show success feedback
            Alert.alert('Éxito', 'Producto eliminado correctamente');
          }
        }
      ]
    );
  }, [deleteProduct]);

  const renderInventoryItem = useCallback(({ item }: { item: Product }) => {
    return (
      <InventoryListItem
        item={item}
        canManageInventory={can.manageInventory}
        canViewCosts={can.viewCosts}
        formatCurrency={formatCurrency}
        formatVolume={formatVolume}
        formatWeight={formatWeight}
        getUnitLabel={getUnitLabel}
        onDelete={handleDeleteItem}
      />
    );
  }, [can.manageInventory, can.viewCosts, formatCurrency, formatVolume, formatWeight, getUnitLabel, handleDeleteItem]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por nombre, marca o categoría..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <View style={styles.headerButtons}>
          {can.viewReports && (
            <TouchableOpacity 
              style={styles.reportsButton}
              onPress={() => router.push('/inventory/reports')}
            >
              <BarChart size={20} color={Colors.light.primary} />
              <Text style={styles.reportsButtonText}>Reportes</Text>
            </TouchableOpacity>
          )}
          {can.manageInventory && (
            <Link href="/inventory/new" asChild>
              <TouchableOpacity style={styles.addButton}>
                <PlusCircle size={20} color="white" />
                <Text style={styles.addButtonText}>Nuevo</Text>
              </TouchableOpacity>
            </Link>
          )}
        </View>
      </View>

      <Text style={styles.description}>
        Gestiona tu inventario de productos. {configuration.inventoryControlLevel === 'smart-cost' && 'Los costos se calcularán automáticamente en cada servicio.'}
      </Text>

      {products.length === 0 ? (
        <View style={styles.emptyState}>
          <Package size={64} color={Colors.light.lightGray} />
          <Text style={styles.emptyStateTitle}>No hay productos en el inventario</Text>
          <Text style={styles.emptyStateText}>
            Comienza agregando productos a tu inventario
          </Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={filteredInventory}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          renderItem={renderInventoryItem}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          removeClippedSubviews={true}
          ListEmptyComponent={
            <View style={styles.emptySearchState}>
              <Text style={styles.emptySearchText}>No se encontraron productos</Text>
            </View>
          }
        />
      )}

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    padding: spacing.lg,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  headerButtons: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F7",
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    marginRight: spacing.sm,
    height: 48,
    borderWidth: 0,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  reportsButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F7",
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  reportsButtonText: {
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
    marginLeft: spacing.xs,
    fontSize: typography.sizes.sm,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...shadows.sm,
  },
  addButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.xs,
  },
  description: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  listContainer: {
    paddingBottom: spacing.lg,
  },
  emptyState: {
    alignItems: "center",
    backgroundColor: Colors.light.card,
    padding: spacing.xl,
    borderRadius: radius.lg,
    ...shadows.sm,
    marginTop: spacing.lg,
  },
  emptyStateText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySearchState: {
    alignItems: "center",
    padding: 40,
  },
  emptySearchText: {
    fontSize: 16,
    color: Colors.light.gray,
  },
});