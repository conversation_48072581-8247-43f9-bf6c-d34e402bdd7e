import React, { useRef, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Switch, TextInput, ActivityIndicator, Alert } from 'react-native';
import { ChevronRight, Zap } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { hairColorMap } from '@/constants/hair-colors';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { useFormulation } from '@/src/service/hooks/useFormulation';
import { useSalonConfigStore } from '@/stores/salon-config-store';

// Import existing components
import ViabilityIndicator from '@/components/ViabilityIndicator';
import FormulaCostBreakdown from '@/components/FormulaCostBreakdown';
import { BrandSelectionModal } from '@/components/BrandSelectionModal';
import FormulaDisplay from '@/components/formulation/FormulaDisplay';
import AIResultNotification from '@/components/AIResultNotification';

// Import new enhanced components
import { MaterialsSummaryCard } from '@/components/formulation/MaterialsSummaryCard';
import { EnhancedFormulationView } from '@/components/formulation/EnhancedFormulationView';
import { QuickAdjustPanel, AdjustmentType } from '@/components/formulation/QuickAdjustPanel';
import FormulaTips from '@/components/formulation/FormulaTips';
import HairRecommendations from '@/components/HairRecommendations';

// Import specialized display components
import { GlobalFormulaDisplay, ZonalFormulaDisplay } from '@/src/service/components/displays';

interface FormulationStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
  analysisResult?: any; // AI analysis result from diagnosis
}

export const FormulationStep: React.FC<FormulationStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
  onSave,
  onSaveSilent,
  analysisResult
}) => {
  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);
  const hasShownNotificationRef = useRef(false);
  
  const {
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,
    formulaCost,
    viabilityAnalysis,
    generateFormulaWithAI,
    analyzeViability,
    formulationData
  } = useFormulation();

  // Initialize state from parent data only once
  React.useEffect(() => {
    if (!selectedBrand && data.selectedBrand) {
      setSelectedBrand(data.selectedBrand);
    }
    if (!selectedLine && data.selectedLine) {
      setSelectedLine(data.selectedLine);
    }
    if (!formula && data.formula) {
      setFormula(data.formula);
      setIsFormulaFromAI(data.isFormulaFromAI);
    }
  }, []); // Empty dependency array - only run once

  // Update parent when specific state changes (avoiding infinite loops)
  React.useEffect(() => {
    if (formula || formulaCost || viabilityAnalysis) {
      onUpdate({
        selectedBrand,
        selectedLine,
        formula,
        isFormulaFromAI,
        formulaCost,
        viabilityAnalysis,
        formulaData: formulationData // Include structured formulation data with correct name
      });
    }
  }, [formula, formulaCost, viabilityAnalysis, formulationData]); // Added formulationData to dependencies

  // Analyze viability when desired analysis changes
  React.useEffect(() => {
    if (data.desiredAnalysisResult && analysisResult) {
      const viability = analyzeViability(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis
      );
      onUpdate({ viabilityAnalysis: viability });
    }
  }, [data.desiredAnalysisResult, analysisResult, data.zoneColorAnalysis]);
  
  // Show notification and scroll when formula is generated
  useEffect(() => {
    if ((formula || formulationData) && scrollRef.current && !hasShownNotificationRef.current) {
      // Count what was generated
      let fieldsCount = 0;
      if (formula) fieldsCount++;
      if (formulationData) {
        if (formulationData.products?.length) fieldsCount += formulationData.products.length;
        if (formulationData.steps?.length) fieldsCount += formulationData.steps.length;
        if (formulationData.techniques?.length) fieldsCount += formulationData.techniques.length;
      }
      
      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;
      
      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      // Smooth scroll to formula section
      setTimeout(() => {
        scrollRef.current?.scrollTo({ y: 400, animated: true });
      }, 300);
    }
    
    // Reset the flag when formula is cleared
    if (!formula && !formulationData) {
      hasShownNotificationRef.current = false;
    }
  }, [formula, formulationData]);

  const handleGenerateFormula = async (adjustment?: AdjustmentType) => {
    try {
      // If there's an adjustment, include it in the AI request
      let adjustmentContext = '';
      if (adjustment) {
        if (adjustment.type === 'temperature') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'cooler' ? 'fría' : 'cálida'}`;
        } else if (adjustment.type === 'brightness') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'lighter' ? 'clara' : 'oscura'}`;
        } else if (adjustment.type === 'oxidant') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value === 'increase' ? 'Aumentar' : 'Disminuir'} el volumen del oxidante`;
        } else if (adjustment.type === 'custom') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value}`;
        } else if (adjustment.type === 'brand') {
          // Brand change is handled through existing state
        }
      }

      const message = await generateFormulaWithAI(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis,
        data.clientId || undefined,
        adjustmentContext
      );
      
      // Save silently when formula is generated
      onSaveSilent?.();
      
      if (message) {
        console.log(message);
      }
    } catch (error) {
      console.error('Error generating formula:', error);
    }
  };

  const handleAdjustFormula = async (adjustment: AdjustmentType) => {
    if (adjustment.type === 'brand') {
      // Brand change is handled separately
      return;
    }
    
    // Regenerate formula with adjustment
    await handleGenerateFormula(adjustment);
  };

  const handleBrandSelection = (brand: string, line: string) => {
    if (brandModalType === 'main') {
      setSelectedBrand(brand);
      setSelectedLine(line);
      onSave?.();
      
      // If in conversion mode and target brand equals source brand, clear source
      if (conversionMode && brand === originalBrand) {
        setOriginalBrand("");
        setOriginalLine("");
        Alert.alert("Atención", "La marca de destino no puede ser igual a la marca de origen");
      }
    } else {
      // Conversion mode - setting original brand
      if (brand === selectedBrand) {
        Alert.alert("Atención", "La marca de origen debe ser diferente a tu marca destino");
        return;
      }
      setOriginalBrand(brand);
      setOriginalLine(line);
    }
    setShowBrandModal(false);
  };

  // Adaptive formula rendering based on selected technique
  const renderFormulationUI = () => {
    const technique = data.desiredAnalysisResult?.general?.technique;
    
    if (!formula) {
      return null; // No formula to display yet
    }

    // Common props for all display components
    const commonProps = {
      formula,
      selectedBrand,
      selectedLine,
      technique: technique || 'full_color',
      formulationData,
      clientName: data.client?.name,
      isFromAI: isFormulaFromAI,
      onEdit: setFormula,
      viabilityAnalysis,
      currentLevel: analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5,
      targetLevel: parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || "7") || 7
    };

    switch (technique) {
      case 'full_color':
      case 'ombre':
      case 'color_correction':
      case 'money_piece':
        return <GlobalFormulaDisplay {...commonProps} />;

      case 'balayage':
      case 'highlights':
      case 'foilyage':
      case 'babylights':
      case 'chunky_highlights':
      case 'reverse_balayage':
        return <ZonalFormulaDisplay {...commonProps} />;

      default:
        // Fallback to zonal display for unknown techniques
        return <ZonalFormulaDisplay {...commonProps} />;
    }
  };

  return (
    <>
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Fórmula generada con IA"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to formula display with more noticeable offset
            scrollRef.current.scrollTo({ y: 500, animated: true });
            // Add haptic feedback
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />
      
      <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Formulación Ultra-Inteligente</Text>
        {data.client && (
          <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
        )}
        
        {/* Color Transition Summary */}
        {analysisResult && data.desiredAnalysisResult && (
          <View style={styles.colorTransitionCard}>
            <View style={styles.colorTransitionHeader}>
              <Text style={styles.colorTransitionTitle}>Transformación de Color</Text>
            </View>
            <View style={styles.colorTransitionContent}>
              <View style={styles.colorBox}>
                <View style={[styles.colorSample, { backgroundColor: hairColorMap[analysisResult.overallTone] || '#8B4513' }]} />
                <Text style={styles.colorLevel}>Nivel {analysisResult.averageLevel || analysisResult.averageDepthLevel || 5}</Text>
                <Text style={styles.colorTone}>{analysisResult.overallTone || 'Castaño'}</Text>
              </View>
              <ChevronRight size={24} color={Colors.light.primary} style={styles.arrowIcon} />
              <View style={styles.colorBox}>
                <View style={[styles.colorSample, { backgroundColor: hairColorMap[data.desiredAnalysisResult.general.overallTone] || '#F5DEB3' }]} />
                <Text style={styles.colorLevel}>Nivel {data.desiredAnalysisResult.general.overallLevel || 8}</Text>
                <Text style={styles.colorTone}>{data.desiredAnalysisResult.general.overallTone || 'Rubio'}</Text>
              </View>
            </View>
            {data.desiredAnalysisResult.general.technique && (
              <View style={styles.techniqueIndicator}>
                <Text style={styles.techniqueLabel}>Técnica: {data.desiredAnalysisResult.general.technique}</Text>
              </View>
            )}
          </View>
        )}

        <View style={styles.formGroup}>
          <Text style={styles.label}>Marca</Text>
          <TouchableOpacity 
            style={styles.selectContainer}
            onPress={() => {
              setBrandModalType('main');
              setShowBrandModal(true);
            }}
          >
            <Text style={styles.selectText}>{selectedBrand}</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Línea</Text>
          <TouchableOpacity 
            style={styles.selectContainer}
            onPress={() => {
              setBrandModalType('main');
              setShowBrandModal(true);
            }}
          >
            <Text style={styles.selectText}>{selectedLine}</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        </View>
        
        {/* Brand Conversion Section */}
        <View style={[styles.conversionSection, conversionMode && styles.conversionSectionActive]}>
          <TouchableOpacity 
            style={styles.conversionHeader}
            activeOpacity={0.7}
            onPress={() => setConversionMode(!conversionMode)}
          >
            <View style={styles.conversionTitleContainer}>
              <Text style={styles.conversionTitle}>Adaptar fórmula de otra marca</Text>
              <Text style={styles.conversionSubtitle}>Obtén el equivalente en tu marca</Text>
            </View>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
              thumbColor={conversionMode ? Colors.light.primary : Colors.light.gray}
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={setConversionMode}
              value={conversionMode}
              style={styles.conversionSwitch}
            />
          </TouchableOpacity>
          
          {conversionMode && (
            <View style={styles.conversionContent}>
              {/* Formula Input First */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>¿Cuál es la fórmula?</Text>
                <TextInput
                  style={[styles.input, styles.textArea, styles.conversionFormulaInput]}
                  value={originalFormula}
                  onChangeText={setOriginalFormula}
                  placeholder="Ej: 7.31 + 8.34 (2:1) • Oxidante 20 vol • 35 min"
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>

              <TouchableOpacity 
                style={styles.conversionBrandContainer}
                onPress={() => {
                  setBrandModalType('conversion');
                  setShowBrandModal(true);
                }}
              >
                <Text style={styles.conversionLabel}>¿De qué marca es la fórmula?</Text>
                <View style={styles.conversionSelectContainer}>
                  <Text style={styles.selectText}>
                    {originalBrand || "Seleccionar marca..."}
                  </Text>
                  <ChevronRight size={16} color={Colors.light.gray} />
                </View>
              </TouchableOpacity>
              
              {originalBrand && (
                <TouchableOpacity 
                  style={styles.conversionBrandContainer}
                  onPress={() => {
                    setBrandModalType('conversion');
                    setShowBrandModal(true);
                  }}
                >
                  <Text style={styles.conversionLabel}>¿Qué línea o producto?</Text>
                  <View style={styles.conversionSelectContainer}>
                    <Text style={styles.selectText}>
                      {originalLine || "Seleccionar línea..."}
                    </Text>
                    <ChevronRight size={16} color={Colors.light.gray} />
                  </View>
                </TouchableOpacity>
              )}
              
              {originalBrand && originalLine && (
                <View style={styles.conversionInfoBox}>
                  <Text style={styles.conversionInfoText}>
                    ✨ Se adaptará a {selectedBrand} {selectedLine}
                  </Text>
                  <Text style={styles.conversionInfoSubtext}>
                    Manteniendo el mismo resultado de color
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        <TouchableOpacity 
          style={[styles.aiButton, isGeneratingFormula && styles.aiButtonDisabled]}
          onPress={() => handleGenerateFormula()}
          disabled={isGeneratingFormula}
        >
          {isGeneratingFormula ? (
            <View style={styles.aiButtonLoading}>
              <ActivityIndicator size="small" color="white" />
              <Text style={styles.aiButtonText}>Generando fórmula...</Text>
            </View>
          ) : (
            <View style={styles.aiButtonContent}>
              <Zap size={20} color="white" />
              <Text style={styles.aiButtonText}>
                {conversionMode && originalBrand && originalLine ? 'Convertir y Generar Fórmula' : 'Generar Fórmula Ultra-Inteligente'}
              </Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Quick Adjust Panel - Always visible after formula generation */}
        {formula && (
          <QuickAdjustPanel
            onAdjustFormula={handleAdjustFormula}
            onChangeBrand={(brand, line) => {
              setSelectedBrand(brand);
              setSelectedLine(line);
              handleGenerateFormula({ type: 'brand', brand, line });
            }}
            currentBrand={selectedBrand}
            currentLine={selectedLine}
            isGenerating={isGeneratingFormula}
          />
        )}

        {/* Adaptive Formulation UI - Renders appropriate component based on technique */}
        {renderFormulationUI()}

        {/* Intelligent Tips and Recommendations - Only show after formula is generated */}
        {formula && analysisResult && data.desiredAnalysisResult && (
          <>
            <FormulaTips 
              analysis={analysisResult}
              technique={data.desiredAnalysisResult.general?.technique || 'full_color'}
              targetLevel={parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || "7") || 7}
            />
            
            <HairRecommendations 
              analysis={analysisResult}
              desiredAnalysis={data.desiredAnalysisResult}
            />
          </>
        )}

        {/* Viability Analysis */}
        {viabilityAnalysis && (
          <ViabilityIndicator analysis={viabilityAnalysis} />
        )}

        {/* Formula Cost Breakdown */}
        {formulaCost && (
          <FormulaCostBreakdown 
            cost={formulaCost} 
            isRealCost={formulaCost.hasAllRealCosts === true && useSalonConfigStore.getState().configuration.inventoryControlLevel !== 'solo-formulas'}
          />
        )}

        {/* Continue Button */}
        {formula && (
          <TouchableOpacity
            style={styles.continueButton}
            onPress={onNext}
          >
            <Text style={styles.continueButtonText}>Continuar al Resultado Final</Text>
          </TouchableOpacity>
        )}

        {/* Brand Selection Modal */}
        <BrandSelectionModal
          visible={showBrandModal}
          onClose={() => setShowBrandModal(false)}
          onSelectBrand={handleBrandSelection}
          currentBrand={brandModalType === 'main' ? selectedBrand : originalBrand}
          currentLine={brandModalType === 'main' ? selectedLine : originalLine}
          title={brandModalType === 'main' ? "Seleccionar Marca y Línea" : "Marca y Línea Original"}
          isConversionMode={brandModalType === 'conversion'}
          sourceBrand={brandModalType === 'conversion' ? originalBrand : undefined}
          sourceLine={brandModalType === 'conversion' ? originalLine : undefined}
        />
      </View>
    </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  colorTransitionCard: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  colorTransitionHeader: {
    marginBottom: 16,
  },
  colorTransitionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
  },
  colorTransitionContent: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
  colorBox: {
    alignItems: "center",
  },
  colorSample: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
    borderWidth: 3,
    borderColor: Colors.light.border,
  },
  colorLevel: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.light.text,
  },
  colorTone: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  arrowIcon: {
    marginHorizontal: 20,
  },
  techniqueIndicator: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  techniqueLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
    textAlign: "center",
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectText: {
    fontSize: 15,
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  conversionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  conversionSectionActive: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  conversionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    minHeight: 60,
  },
  conversionTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  conversionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  conversionSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  conversionSwitch: {
    transform: [{ scale: 0.8 }],
  },
  conversionContent: {
    marginTop: 8,
  },
  conversionBrandContainer: {
    marginBottom: 16,
  },
  conversionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
    marginBottom: 8,
  },
  conversionSelectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  conversionFormulaInput: {
    minHeight: 140,
    paddingTop: 12,
  },
  conversionInfoBox: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  conversionInfoText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 4,
  },
  conversionInfoSubtext: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  aiButton: {
    backgroundColor: Colors.light.accent,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: Colors.light.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  aiButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  aiButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  aiButtonLoading: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  aiButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  continueButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 20,
    marginBottom: 10,
  },
  continueButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});