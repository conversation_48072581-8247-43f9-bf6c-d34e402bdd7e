import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Check, Cloud } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface AutoSaveIndicatorProps {
  lastSaved?: Date;
  isSaving?: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({ 
  lastSaved, 
  isSaving = false 
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [showSaved, setShowSaved] = useState(false);
  const mountedRef = useRef(true);

  // Show/hide animation when saving state changes
  useEffect(() => {
    if (isSaving) {
      // Fade in when saving
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else if (showSaved) {
      // Show saved state briefly
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Hide after 2 seconds
      const timer = setTimeout(() => {
        if (mountedRef.current) {
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            if (mountedRef.current) {
              setShowSaved(false);
            }
          });
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isSaving, showSaved, fadeAnim]);

  // Track when saving completes
  useEffect(() => {
    if (!isSaving && lastSaved && mountedRef.current) {
      setShowSaved(true);
    }
  }, [isSaving, lastSaved]);
  
  // Track mounted state
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  if (!isSaving && !showSaved) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {isSaving ? (
        <>
          <Cloud size={14} color={Colors.light.gray} />
          <Text style={styles.savingText}>Guardando...</Text>
        </>
      ) : (
        <>
          <Check size={14} color={Colors.light.success} />
          <Text style={styles.savedText}>Guardado</Text>
        </>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.light.background,
    gap: 4,
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
  },
  savingText: {
    color: Colors.light.gray,
  },
  savedText: {
    color: Colors.light.success,
  },
});