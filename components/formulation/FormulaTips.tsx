import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { AlertTriangle, Info, CheckCircle, Lightbulb, Thermometer, Clock } from 'lucide-react-native';
import { ContextualTip } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

interface FormulaTipsProps {
  analysis: any; // Análisis del diagnóstico actual
  technique: string; // Técnica seleccionada
  targetLevel: number; // Nivel objetivo del servicio
}

export default function FormulaTips({ analysis, technique, targetLevel }: FormulaTipsProps) {
  // Generar consejos contextuales dinámicos
  const generateContextualTips = React.useMemo(() => {
    const tips: ContextualTip[] = [];

    // Consejos basados en canas
    if (analysis?.hasGreyHair || analysis?.averageDepthLevel <= 3) {
      tips.push({
        id: 'grey-coverage',
        type: 'warning',
        title: 'Cobertura de Canas',
        description: 'Asegurate de usar fórmula con buena cobertura de canas. Considera pre-pigmentación si hay más de 50% de canas.'
      });
    }

    // Consejos basados en técnica
    if (technique === 'balayage' || technique === 'highlights') {
      tips.push({
        id: 'strand-test',
        type: 'tip',
        title: 'Prueba de Mecha',
        description: 'Siempre realiza prueba de mecha antes de aplicar. Para balayage, prueba en sección menos visible.'
      });
    }

    // Consejos por daño capilar
    if (analysis?.zonePhysicalAnalysis?.roots?.damage === 'Severo' || 
        analysis?.zonePhysicalAnalysis?.mids?.damage === 'Severo' ||
        analysis?.zonePhysicalAnalysis?.ends?.damage === 'Severo') {
      tips.push({
        id: 'damaged-hair',
        type: 'warning',
        title: 'Cabello Dañado',
        description: 'Usa oxidante de menor volumen y considera tratamiento reconstrutivo antes del servicio.'
      });
    }

    // Consejos por gran diferencia de nivel
    const currentLevel = analysis?.averageDepthLevel || analysis?.averageLevel || 5;
    // Ahora usamos el targetLevel que viene como prop
    if (targetLevel - currentLevel >= 3) {
      tips.push({
        id: 'lightening',
        type: 'warning',
        title: 'Aclarado Intenso',
        description: 'Diferencia de más de 3 niveles. Considera hacer en 2 sesiones para mantener integridad capilar.'
      });
    }

    // Consejos generales de técnica
    if (technique === 'full_color') {
      tips.push({
        id: 'application-order',
        type: 'info',
        title: 'Orden de Aplicación',
        description: 'Aplica primero en medios y puntas, deja raíces para el final para control de temperatura.'
      });
    }

    // Consejo de temperatura
    tips.push({
      id: 'temperature',
      type: 'info',
      title: 'Control de Temperatura',
      description: 'Mantén ambiente entre 20-25°C. Evita fuentes de calor directo durante procesamiento.'
    });

    return tips;
  }, [analysis, technique, targetLevel]);

  const tips = generateContextualTips;
  const getIcon = (type: ContextualTip['type']) => {
    const iconProps = { size: 20, color: 'white' };
    
    switch (type) {
      case 'warning':
        return <AlertTriangle {...iconProps} />;
      case 'info':
        return <Info {...iconProps} />;
      case 'success':
        return <CheckCircle {...iconProps} />;
      case 'tip':
      default:
        return <Lightbulb {...iconProps} />;
    }
  };

  const getIconByTitle = (title: string) => {
    const iconProps = { size: 20, color: 'white' };
    
    if (title.toLowerCase().includes('temperatura')) {
      return <Thermometer {...iconProps} />;
    }
    if (title.toLowerCase().includes('tiempo') || title.toLowerCase().includes('control')) {
      return <Clock {...iconProps} />;
    }
    return null;
  };

  const getStyles = (type: ContextualTip['type']) => {
    switch (type) {
      case 'warning':
        return {
          container: styles.warningContainer,
          iconBg: styles.warningIconBg,
          title: styles.warningTitle,
        };
      case 'info':
        return {
          container: styles.infoContainer,
          iconBg: styles.infoIconBg,
          title: styles.infoTitle,
        };
      case 'success':
        return {
          container: styles.successContainer,
          iconBg: styles.successIconBg,
          title: styles.successTitle,
        };
      case 'tip':
      default:
        return {
          container: styles.tipContainer,
          iconBg: styles.tipIconBg,
          title: styles.tipTitle,
        };
    }
  };

  const groupedTips = tips.reduce((acc, tip) => {
    if (!acc[tip.type]) {
      acc[tip.type] = [];
    }
    acc[tip.type].push(tip);
    return acc;
  }, {} as Record<ContextualTip['type'], ContextualTip[]>);

  const typeOrder: ContextualTip['type'][] = ['warning', 'info', 'tip', 'success'];

  return (
    <View style={styles.container}>
      {typeOrder.map(type => {
        const tipsOfType = groupedTips[type];
        if (!tipsOfType || tipsOfType.length === 0) return null;

        return (
          <View key={type} style={styles.typeSection}>
            {tipsOfType.map((tip) => {
              const tipStyles = getStyles(tip.type);
              const customIcon = getIconByTitle(tip.title);
              
              return (
                <View key={tip.id} style={[styles.tipCard, tipStyles.container]}>
                  <View style={[styles.iconContainer, tipStyles.iconBg]}>
                    {customIcon || getIcon(tip.type)}
                  </View>
                  <View style={styles.textContainer}>
                    <Text style={[styles.tipTitle, tipStyles.title]}>{tip.title}</Text>
                    <Text style={styles.tipDescription}>{tip.description}</Text>
                  </View>
                </View>
              );
            })}
          </View>
        );
      })}

      {/* Quick Tips Grid */}
      <View style={styles.quickTipsGrid}>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <Thermometer size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>20-25°C</Text>
        </View>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <Clock size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>Revisar c/10min</Text>
        </View>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <CheckCircle size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>Prueba mechón</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 10,
  },
  typeSection: {
    gap: 10,
  },
  tipCard: {
    flexDirection: 'row',
    padding: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
  },
  warningContainer: {
    backgroundColor: '#fff3cd',
    borderLeftColor: '#ffc107',
  },
  infoContainer: {
    backgroundColor: '#d1ecf1',
    borderLeftColor: '#17a2b8',
  },
  successContainer: {
    backgroundColor: '#d4edda',
    borderLeftColor: '#28a745',
  },
  tipContainer: {
    backgroundColor: '#e7e3ff',
    borderLeftColor: '#667eea',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  warningIconBg: {
    backgroundColor: '#ffc107',
  },
  infoIconBg: {
    backgroundColor: '#17a2b8',
  },
  successIconBg: {
    backgroundColor: '#28a745',
  },
  tipIconBg: {
    backgroundColor: '#667eea',
  },
  textContainer: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  warningTitle: {
    color: '#856404',
  },
  infoTitle: {
    color: '#0c5460',
  },
  successTitle: {
    color: '#155724',
  },
  tipDescription: {
    fontSize: 13,
    color: Colors.light.gray,
    lineHeight: 18,
  },
  quickTipsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  quickTip: {
    alignItems: 'center',
  },
  quickTipIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  quickTipText: {
    fontSize: 11,
    color: Colors.light.gray,
    fontWeight: '500',
  },
});