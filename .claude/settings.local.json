{"permissions": {"allow": ["Bash(rg:*)", "Bash(npx tsc:*)", "Bash(npx typescript:*)", "Bash(bun tsc:*)", "Bash(./node_modules/.bin/tsc:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(bunx rork start:*)", "Bash(npm start)", "<PERSON><PERSON>(rork start:*)", "Bash(npx rork start:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git fetch:*)", "Bash(cp:*)", "Bash(grep:*)", "Bash(rm:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(sed:*)", "Bash(git rebase:*)", "<PERSON><PERSON>(tail:*)", "Bash(npm run lint)", "Bash(npm run:*)", "<PERSON><PERSON>(expo start)", "Bash(npx expo:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(find:*)", "Bash(bun x rork:*)", "Bash(git tag:*)", "Bash(git rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(sudo lsof:*)", "Bash(git diff:*)", "Bash(git checkout:*)", "Bash(echo $PATH)", "Bash(git merge:*)", "WebFetch(domain:apps.apple.com)", "<PERSON><PERSON>(watchman watch-del-all:*)", "Bash(npx react-native start:*)", "Bash(EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 npx expo start --lan --clear)", "Bash(npx:*)", "Bash(node:*)", "Bash(for file in components/desired/steps/*.tsx components/diagnosis/steps/*.tsx)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(git revert:*)", "Bash(git stash:*)", "<PERSON><PERSON>(cat:*)", "Bash(awk:*)", "Bash(for:*)", "<PERSON><PERSON>(clear)", "mcp__ide__getDiagnostics", "Bash(git restore:*)", "Bash(git reset:*)", "Bash(wc:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude --version)", "WebFetch(domain:supabase.com)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "<PERSON><PERSON>(claude mcp:*)", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__generate_typescript_types", "mcp__supabase__search_docs", "mcp__supabase__get_logs", "<PERSON><PERSON>(chmod:*)", "Bash(/Users/<USER>/rork-salonier-copilot--asistente-de-coloraci-n-capilar-con-ia/scripts/apply-migrations.sh)", "WebFetch(domain:github.com)", "mcp__supabase__list_migrations", "mcp__supabase__list_edge_functions", "mcp__supabase__deploy_edge_function", "mcp__supabase__get_advisors", "<PERSON><PERSON>(echo:*)", "mcp__supabase__list_extensions", "<PERSON><PERSON>(test:*)", "Bash(./scripts/test-edge-function-simple.sh:*)", "Ba<PERSON>(expo start:*)", "<PERSON><PERSON>(od:*)", "Bash(./scripts/deploy-edge-function.sh:*)", "Bash(supabase link:*)", "Bash(supabase functions deploy:*)", "Bash(npm test:*)", "Bash(timeout 30 npm run dev:*)", "Bash(tmux new-window:*)", "<PERSON><PERSON>(tmux kill-session:*)", "<PERSON><PERSON>(tmux list-sessions:*)", "Bash(./schedule_with_note.sh:*)", "Bash(./Tmux-Orchestrator-main/schedule_with_note.sh:*)", "Bash(./Tmux-Orchestrator-main/send-claude-message.sh:*)", "Bash(tmux list-windows:*)", "Ba<PERSON>(expo prebuild:*)", "Bash(git branch:*)", "Bash(gh pr list:*)", "Bash(./scripts/optimize-images.sh:*)", "Bash(./scripts/optimize-images-final.sh:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}