import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { FormulationConsumption, Product } from '@/types/inventory';
import { ColorFormula } from '@/types/formulation';
import { ProductNormalizationService } from './productNormalizationService';
import { ProductNamingService } from './productNamingService';
import { logger } from '../utils/logger';

interface ProductMatch {
  product: Product;
  matchScore: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
  confidence: number; // Nueva propiedad para niveles de confianza
}

interface StructuredProductSearch {
  brand?: string;
  line?: string;
  type?: string;
  shade?: string;
  name?: string; // Legacy fallback
}

export class InventoryConsumptionService {
  /**
   * Encuentra productos usando campos estructurados con normalización inteligente
   */
  static findMatchingProductsStructured(search: StructuredProductSearch): ProductMatch[] {
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;
    
    const matches: ProductMatch[] = [];
    
    // Primero buscar en mappings guardados si tenemos un nombre
    if (search.name) {
      const savedMapping = inventoryStore.getProductMapping(search.name);
      if (savedMapping && savedMapping.confidence >= 80) {
        const mappedProduct = products.find(p => p.id === savedMapping.inventoryProductId);
        if (mappedProduct) {
          logger.info('Usando mapping guardado para:', search.name);
          // Incrementar uso del mapping
          inventoryStore.incrementMappingUsage(search.name);
          
          matches.push({
            product: mappedProduct,
            matchScore: 100,
            matchType: 'exact',
            confidence: savedMapping.confidence
          });
          return matches;
        }
      }
    }
    
    // Normalizar búsqueda si es necesario
    let normalizedSearch = search;
    if (search.name && !search.brand && !search.type) {
      // Si solo tenemos nombre, intentar parsearlo
      const parsed = ProductNormalizationService.parseProduct(search.name);
      normalizedSearch = {
        ...search,
        brand: parsed.brand || search.brand,
        line: parsed.line || search.line,
        type: parsed.type || search.type,
        shade: parsed.shade || search.shade,
      };
    }
    
    products.forEach(product => {
      // Normalizar producto del inventario
      const normalizedProduct = ProductNormalizationService.parseProduct(
        product.displayName || product.name
      );
      
      // Usar ambos servicios para calcular similitud
      const normalizationScore = ProductNormalizationService.calculateSimilarity(
        {
          brand: normalizedSearch.brand || '',
          line: normalizedSearch.line || '',
          type: normalizedSearch.type || '',
          shade: normalizedSearch.shade || '',
          original: search.name || ''
        },
        {
          brand: product.brand || normalizedProduct.brand,
          line: product.line || normalizedProduct.line,
          type: product.type || normalizedProduct.type,
          shade: product.shade || normalizedProduct.shade,
          original: product.displayName || product.name
        }
      );
      
      // También calcular similitud usando el servicio de naming
      const namingScore = ProductNamingService.calculateSimilarity(
        search.name || '',
        product.displayName || product.name
      );
      
      // Usar el mejor score de los dos métodos
      const similarity = Math.max(normalizationScore, namingScore);
      
      if (similarity >= 60) {
        let matchType: ProductMatch['matchType'] = 'fuzzy';
        let confidence = similarity;
        
        if (similarity === 100) {
          matchType = 'exact';
          confidence = 100;
        } else if (similarity >= 80) {
          matchType = 'partial';
          confidence = 90;
        } else {
          confidence = 60;
        }
        
        matches.push({ 
          product, 
          matchScore: similarity, 
          matchType,
          confidence 
        });
      }
    });
    
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Encuentra productos en el inventario que coincidan con el nombre dado (método legacy)
   */
  static findMatchingProducts(productName: string, brand?: string): ProductMatch[] {
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;
    
    const normalizedName = this.normalizeProductName(productName);
    const normalizedBrand = brand ? brand.toLowerCase().trim() : '';
    
    const matches: ProductMatch[] = [];
    
    products.forEach(product => {
      const productNormalized = this.normalizeProductName(product.name);
      const brandNormalized = product.brand.toLowerCase().trim();
      
      let matchScore = 0;
      let matchType: ProductMatch['matchType'] = 'fuzzy';
      
      // Exact match
      if (productNormalized === normalizedName) {
        matchScore = 100;
        matchType = 'exact';
      }
      // Contains match
      else if (productNormalized.includes(normalizedName) || normalizedName.includes(productNormalized)) {
        matchScore = 80;
        matchType = 'partial';
      }
      // Common variations
      else if (this.areProductsSimilar(productNormalized, normalizedName)) {
        matchScore = 60;
        matchType = 'fuzzy';
      }
      
      // Brand matching bonus
      if (normalizedBrand && brandNormalized === normalizedBrand) {
        matchScore += 20;
      }
      
      if (matchScore > 50) {
        // Calcular confianza basada en el score
        let confidence = 60; // Por defecto fuzzy
        if (matchScore >= 100) {
          confidence = 100;
        } else if (matchScore >= 80) {
          confidence = 90;
        }
        
        matches.push({ product, matchScore, matchType, confidence });
      }
    });
    
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }
  
  /**
   * Normaliza nombres de productos para mejor matching
   */
  private static normalizeProductName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/oxidante|developer|peróxido|peroxide/gi, 'oxidante')
      .replace(/decolorante|bleach|polvo/gi, 'decolorante')
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '');
  }
  
  /**
   * Verifica si dos productos son similares basándose en variaciones comunes
   */
  private static areProductsSimilar(name1: string, name2: string): boolean {
    const variations = [
      ['oxidante', 'developer', 'peroxide', 'peróxido', 'activador'],
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener'],
      ['olaplex', 'plex', 'bond'],
      ['10 vol', '3%', '10v'],
      ['20 vol', '6%', '20v'],
      ['30 vol', '9%', '30v'],
      ['40 vol', '12%', '40v'],
    ];
    
    for (const group of variations) {
      const hasName1 = group.some(v => name1.includes(v));
      const hasName2 = group.some(v => name2.includes(v));
      if (hasName1 && hasName2) return true;
    }
    
    return false;
  }
  
  /**
   * Calcula el costo de una formulación desde texto usando parseFormulaTextToProducts
   */
  static async calculateFormulationCostFromText(formulaText: string): Promise<FormulationConsumption> {
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();
    
    // Import parseFormulaTextToProducts at runtime to avoid circular dependency
    const { parseFormulaTextToProducts } = await import('@/utils/parseFormula');
    const products = parseFormulaTextToProducts(formulaText);
    
    const items: FormulationConsumption['items'] = [];
    let totalCost = 0;
    let hasInsufficientStock = false;
    const insufficientProducts: string[] = [];
    let hasAllRealCosts = true; // Track if all products have real costs
    const missingPriceProducts: string[] = [];
    
    for (const product of products) {
      // Try structured matching first if fields are available
      let matches: ProductMatch[] = [];
      
      // Check if product has structured fields (from JSON formula)
      if ((product as any).brand || (product as any).type || (product as any).shade) {
        matches = this.findMatchingProductsStructured({
          brand: (product as any).brand,
          line: (product as any).line,
          type: (product as any).type,
          shade: (product as any).shade,
        });
      }
      
      // Fallback to name-based matching if no structured match found
      if (matches.length === 0) {
        matches = this.findMatchingProducts(product.name);
      }
      
      const bestMatch = matches[0];
      
      if (bestMatch && bestMatch.matchScore > 60) {
        const inventoryProduct = bestMatch.product;
        const amount = product.amount;
        const unitCost = inventoryProduct.costPerUnit;
        const itemCost = amount * unitCost;
        
        const isStockSufficient = inventoryProduct.currentStock >= amount;
        if (!isStockSufficient) {
          hasInsufficientStock = true;
          insufficientProducts.push(inventoryProduct.name);
        }
        
        items.push({
          productId: inventoryProduct.id,
          productName: inventoryProduct.name,
          amount,
          unit: inventoryProduct.unitType,
          unitCost,
          totalCost: itemCost,
          availableStock: inventoryProduct.currentStock,
          isStockSufficient,
        });
        
        totalCost += itemCost;
      } else {
        // Product not found in inventory
        hasAllRealCosts = false;
        missingPriceProducts.push(product.name);
        
        // Only use estimated cost if in 'solo-formulas' mode
        const estimatedCost = configStore.configuration.inventoryControlLevel === 'solo-formulas' 
          ? this.getEstimatedCost(product.name, product.amount)
          : 0;
        
        items.push({
          productId: '',
          productName: `${product.name} (Estimado)`,
          amount: product.amount,
          unit: product.unit,
          unitCost: estimatedCost / product.amount,
          totalCost: estimatedCost,
          availableStock: 0,
          isStockSufficient: false,
        });
        
        totalCost += estimatedCost;
      }
    }
    
    return {
      formulationId: '',
      items,
      totalCost,
      hasInsufficientStock,
      insufficientProducts,
      hasAllRealCosts,
      missingPriceProducts,
    };
  }
  
  /**
   * Calcula el costo de una formulación usando precios del inventario
   */
  static async calculateFormulationCost(formula: ColorFormula): Promise<FormulationConsumption> {
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();
    
    const items: FormulationConsumption['items'] = [];
    let totalCost = 0;
    let hasInsufficientStock = false;
    const insufficientProducts: string[] = [];
    
    // Procesar cada producto en la fórmula
    const formulaItems = this.parseFormula(formula);
    
    for (const item of formulaItems) {
      const matches = this.findMatchingProducts(item.name, item.brand);
      const bestMatch = matches[0];
      
      if (bestMatch && bestMatch.matchScore > 60) {
        const product = bestMatch.product;
        const amount = item.amount;
        const unitCost = product.costPerUnit;
        const itemCost = amount * unitCost;
        
        const isStockSufficient = product.currentStock >= amount;
        if (!isStockSufficient) {
          hasInsufficientStock = true;
          insufficientProducts.push(product.name);
        }
        
        items.push({
          productId: product.id,
          productName: product.name,
          amount,
          unit: product.unitType,
          unitCost,
          totalCost: itemCost,
          availableStock: product.currentStock,
          isStockSufficient,
        });
        
        totalCost += itemCost;
      } else {
        // Si no se encuentra el producto, usar precio estimado
        const estimatedCost = this.getEstimatedCost(item.name, item.amount);
        
        items.push({
          productId: '',
          productName: `${item.name} (Estimado)`,
          amount: item.amount,
          unit: item.unit || 'g',
          unitCost: estimatedCost / item.amount,
          totalCost: estimatedCost,
          availableStock: 0,
          isStockSufficient: false,
        });
        
        totalCost += estimatedCost;
      }
    }
    
    return {
      formulationId: formula.id || '',
      items,
      totalCost,
      hasInsufficientStock,
      insufficientProducts,
    };
  }
  
  /**
   * Parsea una fórmula para extraer productos y cantidades
   */
  private static parseFormula(formula: ColorFormula): Array<{
    name: string;
    brand?: string;
    line?: string;
    type?: string;
    shade?: string;
    amount: number;
    unit?: string;
  }> {
    const items: Array<{ name: string; brand?: string; line?: string; type?: string; shade?: string; amount: number; unit?: string }> = [];
    
    // Check if formula has structured product data (from JSON)
    if ((formula as any).formulationData && (formula as any).formulationData.steps) {
      const formulationData = (formula as any).formulationData;
      formulationData.steps.forEach((step: any) => {
        if (step.mix) {
          step.mix.forEach((product: any) => {
            items.push({
              name: product.productName || '',
              brand: product.brand,
              line: product.line,
              type: product.type,
              shade: product.shade,
              amount: product.quantity || 0,
              unit: product.unit || 'g',
            });
          });
        }
      });
      
      // If we found structured data, return it
      if (items.length > 0) {
        return items;
      }
    }
    
    // Fallback to legacy parsing
    if (formula.colors && formula.colors.length > 0) {
      formula.colors.forEach(color => {
        const amount = typeof color.amount === 'number' ? color.amount : 0;
        items.push({
          name: `${formula.brand} ${color.tone}`,
          brand: formula.brand,
          type: 'Tinte',
          shade: color.tone,
          amount,
          unit: 'g',
        });
      });
    }
    
    // Parsear oxidante
    if (formula.developerVolume) {
      // Calcular la cantidad de oxidante basándose en el ratio
      let developerAmount = 0;
      const totalColorAmount = formula.colors.reduce((sum, color) => sum + (color.amount || 0), 0);
      
      if (formula.developerRatio) {
        // Si el ratio es "1:1.5", "1:2", etc.
        const ratioMatch = formula.developerRatio.match(/1:(\d+\.?\d*)/);
        if (ratioMatch) {
          const ratioMultiplier = parseFloat(ratioMatch[1]);
          developerAmount = totalColorAmount * ratioMultiplier;
        } else {
          // Default 1:1
          developerAmount = totalColorAmount;
        }
      } else {
        // Default 1:1
        developerAmount = totalColorAmount;
      }
      
      items.push({
        name: `Oxidante ${formula.developerVolume} Vol`,
        brand: formula.brand, // Use same brand as colors
        type: 'Oxidante',
        shade: `${formula.developerVolume} vol`,
        amount: developerAmount,
        unit: 'ml',
      });
    }
    
    // Parsear aditivos
    if (formula.additives && formula.additives.length > 0) {
      formula.additives.forEach(additive => {
        const amountMatch = additive.match(/(\d+)\s*(ml|g|gr)/i);
        const amount = amountMatch ? parseFloat(amountMatch[1]) : 10;
        const unit = amountMatch && amountMatch[2] ? amountMatch[2].toLowerCase() : 'ml';
        
        items.push({
          name: additive.replace(/\d+\s*(ml|g|gr)/gi, '').trim(),
          amount,
          unit: unit === 'gr' ? 'g' : unit,
        });
      });
    }
    
    return items;
  }
  
  /**
   * Obtiene un costo estimado para productos no encontrados
   */
  private static getEstimatedCost(productName: string, amount: number): number {
    const normalized = productName.toLowerCase();
    
    if (normalized.includes('oxidante') || normalized.includes('developer')) {
      return amount * 0.005; // €0.005/ml
    }
    if (normalized.includes('decolorante') || normalized.includes('bleach')) {
      return amount * 0.03; // €0.03/g
    }
    if (normalized.includes('olaplex') || normalized.includes('tratamiento')) {
      return amount * 0.5; // €0.50/ml
    }
    
    // Default para tintes
    return amount * 0.15; // €0.15/g
  }
  
  /**
   * Consume productos del inventario para una formulación
   */
  static async consumeFormulation(
    formulationId: string,
    formula: ColorFormula,
    clientName: string
  ): Promise<{ 
    success: boolean; 
    errors: string[];
    consumedProducts: string[];
    notFoundProducts: string[];
    totalConsumed: number;
  }> {
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();
    
    // Verificar si el consumo automático está habilitado
    if (!configStore.configuration.autoConsumption) {
      return { 
        success: true, 
        errors: [], 
        consumedProducts: [],
        notFoundProducts: [],
        totalConsumed: 0
      };
    }
    
    // Verificar nivel de control
    if (configStore.configuration.inventoryControlLevel === 'solo-formulas') {
      return { 
        success: true, 
        errors: [], 
        consumedProducts: [],
        notFoundProducts: [],
        totalConsumed: 0
      };
    }
    
    const errors: string[] = [];
    const consumptions: Array<{ productId: string; quantity: number }> = [];
    const consumedProducts: string[] = [];
    const notFoundProducts: string[] = [];
    
    // Obtener el análisis de consumo
    const analysis = await this.calculateFormulationCost(formula);
    
    // Verificar stock si está configurado
    if (configStore.configuration.requireStockValidation && analysis.hasInsufficientStock) {
      errors.push(`Stock insuficiente para: ${analysis.insufficientProducts.join(', ')}`);
      return { 
        success: false, 
        errors,
        consumedProducts: [],
        notFoundProducts: analysis.insufficientProducts,
        totalConsumed: 0
      };
    }
    
    // Preparar consumos
    for (const item of analysis.items) {
      if (item.productId) {
        consumptions.push({
          productId: item.productId,
          quantity: item.amount,
        });
        consumedProducts.push(`${item.productName} (${item.amount}${item.unit})`);
      } else {
        // Producto no encontrado en inventario
        notFoundProducts.push(item.productName);
      }
    }
    
    // Si no hay productos para consumir, retornar error
    if (consumptions.length === 0) {
      errors.push('No se encontraron productos en el inventario para descontar');
      return { 
        success: false, 
        errors,
        consumedProducts: [],
        notFoundProducts,
        totalConsumed: 0
      };
    }
    
    // Ejecutar consumos
    try {
      await inventoryStore.consumeProducts(consumptions, formulationId, clientName);
      return { 
        success: true, 
        errors: [],
        consumedProducts,
        notFoundProducts,
        totalConsumed: consumptions.length
      };
    } catch (error) {
      errors.push(`Error al consumir productos: ${error}`);
      return { 
        success: false, 
        errors,
        consumedProducts: [],
        notFoundProducts,
        totalConsumed: 0
      };
    }
  }
  
  /**
   * Verifica si hay stock suficiente para una formulación
   */
  static async checkStock(formula: ColorFormula): Promise<{
    hasStock: boolean;
    missingProducts: string[];
  }> {
    const analysis = await this.calculateFormulationCost(formula);
    
    return {
      hasStock: !analysis.hasInsufficientStock,
      missingProducts: analysis.insufficientProducts,
    };
  }
}