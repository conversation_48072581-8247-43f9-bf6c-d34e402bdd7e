export interface Product {
  id: string;
  // Campos estructurados principales
  brand: string;
  line?: string; // Línea de producto (ej: "Vison", "Koleston Perfect")
  type: string; // Tipo de producto (ej: "Tinte", "Oxidante", "Decolorante")
  shade?: string; // Tono/Número (ej: "7", "9.1", "30 vol")
  displayName: string; // Nombre generado automáticamente para mostrar
  
  // Campo legacy para compatibilidad hacia atrás
  name?: string; // @deprecated - Usar displayName en su lugar
  
  category: 'tinte' | 'oxidante' | 'decolorante' | 'matizador' | 'tratamiento' | 'aditivo' | 'pre-pigmentacion' | 'otro';
  currentStock: number;
  minStock: number;
  maxStock?: number;
  unitType: 'ml' | 'g' | 'unidad';
  unitSize: number; // Tamaño del envase (ej: 100ml, 50g)
  purchasePrice: number; // Precio de compra del envase completo
  costPerUnit: number; // Precio por ml o g calculado
  lastUpdated: string;
  isActive: boolean;
  barcode?: string;
  notes?: string;
  supplier?: string;
  lastPurchaseDate?: string;
  colorCode?: string; // Para tintes: código como "7.1", "8/43" - legacy, usar shade
  
  // Campos para sincronización con Supabase
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface StockMovement {
  id: string;
  productId: string;
  type: 'compra' | 'consumo' | 'ajuste' | 'devolución' | 'pérdida' | 'inventario_inicial';
  quantity: number; // Positivo para entradas, negativo para salidas
  date: string;
  userId: string;
  referenceId?: string; // ID de consulta si es consumo
  notes: string | null;
}

export interface PricingConfiguration {
  defaultMarkupPercentage: number; // 0-500%
  roundingPolicy: 'none' | 'nearest' | 'up' | 'down';
  roundingIncrement: number; // 0.5, 1, 5, 10
  minimumServicePrice: number;
  includeTaxInPrice: boolean;
  taxPercentage: number;
  currency: string;
  currencySymbol: string;
  lastUpdated: string;
}

export interface ProductPricing {
  productId: string;
  productName: string;
  brand: string;
  unitPrice: number; // Precio por ml/g
  unitType: 'ml' | 'g';
  lastUpdated: string;
}

export interface InventoryAlert {
  id: string;
  productId: string;
  type: 'low_stock' | 'out_of_stock' | 'expiring' | 'overstock';
  severity: 'low' | 'medium' | 'high' | 'warning' | 'error';
  message: string;
  isActive: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  createdAt: string;
}

export interface ConsumptionAnalysis {
  productId: string;
  productName: string;
  totalConsumed: number;
  totalCost: number;
  averagePerService: number;
  servicesCount: number;
  period: string; // 'daily' | 'weekly' | 'monthly'
  topClients: Array<{
    clientName: string;
    consumptionAmount: number;
    serviceCount: number;
  }>;
}

export interface InventoryReport {
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  overstockCount: number;
  mostUsedProducts: Array<{
    product: Product;
    usageCount: number;
    totalConsumed: number;
  }>;
  leastUsedProducts: Array<{
    product: Product;
    daysSinceLastUse: number;
  }>;
  costByCategory: Array<{
    category: string;
    totalCost: number;
    percentage: number;
  }>;
  generatedAt: string;
}

export interface FormulationConsumption {
  formulationId: string;
  items: Array<{
    productId: string;
    productName: string;
    amount: number;
    unit: 'ml' | 'g';
    unitCost: number;
    totalCost: number;
    availableStock: number;
    isStockSufficient: boolean;
  }>;
  totalCost: number;
  hasInsufficientStock: boolean;
  insufficientProducts: string[];
  hasAllRealCosts: boolean; // true only if ALL products have real prices from inventory
  missingPriceProducts: string[]; // products without configured prices
}

export interface SalonConfiguration {
  businessName: string;
  inventoryControlLevel: 'solo-formulas' | 'smart-cost' | 'control-total';
  pricing: PricingConfiguration;
  notifications: {
    lowStockAlerts: boolean;
    expirationAlerts: boolean;
    restockReminders: boolean;
    appointments?: boolean;
    clientAlerts?: boolean;
    teamUpdates?: boolean;
    businessInsights?: boolean;
  };
  autoConsumption: boolean;
  requireStockValidation: boolean;
  // Regional configuration
  countryCode?: string;
  measurementSystem?: 'metric' | 'imperial';
  language?: string;
  // Lifestyle preferences
  showBudgetOptions?: boolean;
  // Salon address information
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  // Preferred brands
  preferredBrands?: string[];
}

export interface ProductMapping {
  id: string;
  salonId: string;
  aiProductName: string; // Nombre tal como lo genera la IA
  inventoryProductId: string; // ID del producto real en inventario
  confidence: number; // 0-100 nivel de confianza
  usageCount: number; // Veces que se ha usado este mapeo
  createdAt: string;
  updatedAt: string;
}